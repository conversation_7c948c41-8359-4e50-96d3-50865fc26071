* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #000;
    overflow: hidden;
    cursor: crosshair;
}

#container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#instructions {
    position: absolute;
    top: 20px;
    left: 20px;
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 100;
    max-width: 300px;
}

#instructions p {
    margin-bottom: 5px;
}

#loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 18px;
    z-index: 200;
}

#info-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 300;
    max-width: 400px;
    text-align: center;
}

#info-panel.hidden {
    display: none;
}

#artwork-title {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
}

#artist-name {
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
    font-size: 16px;
}

#artwork-description {
    color: #444;
    line-height: 1.6;
    margin-bottom: 20px;
}

#close-info {
    background: #333;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

#close-info:hover {
    background: #555;
}

canvas {
    display: block;
}
