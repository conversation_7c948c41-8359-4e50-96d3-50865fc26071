<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galerie d'Art Virtuelle</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div id="container">
        <div id="info-panel" class="hidden">
            <div id="info-content">
                <h3 id="artwork-title"></h3>
                <p id="artist-name"></p>
                <p id="artwork-description"></p>
                <button id="close-info">Fermer</button>
            </div>
        </div>

        <div id="instructions">
            <p>Utilisez la souris pour regarder autour et WASD pour vous déplacer</p>
            <p>Cliquez sur les œuvres pour plus d'informations</p>
        </div>

        <div id="loading">
            <p>Chargement de la galerie...</p>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://unpkg.com/three@0.128.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="script.js"></script>
</body>

</html>