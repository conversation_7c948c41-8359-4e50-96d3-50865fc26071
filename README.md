# Galerie d'Art Virtuelle

Une galerie d'art virtuelle créée avec Three.js permettant aux utilisateurs de parcourir des œuvres d'art célèbres en 3D.

## Fonctionnalités

- **Navigation 3D** : Utilisez la souris pour regarder autour et naviguer dans la galerie
- **Œuvres d'art réalistes** : Affichage de peintures célèbres avec des cadres 3D
- **Informations interactives** : Cliquez sur les œuvres pour afficher des informations sur l'artiste et l'œuvre
- **Éclairage réaliste** : Système d'éclairage avec spots pour mettre en valeur les œuvres
- **Interface simple** : Design épuré et facile à utiliser

## Œuvres incluses

1. **La Joconde** - Léonard de <PERSON>
2. **La Nuit étoilée** - <PERSON>
3. **Le Cri** - Edvard Munch
4. **Guernica** - <PERSON>

## Comment utiliser

1. Ouvrez `index.html` dans votre navigateur web
2. Attendez que la galerie se charge
3. Utilisez la souris pour naviguer :
   - Clic gauche + glisser : Regarder autour
   - Molette : Zoomer/Dézoomer
4. Cliquez sur une œuvre d'art pour afficher ses informations
5. Cliquez sur "Fermer" pour fermer le panneau d'information

## Technologies utilisées

- **Three.js** : Bibliothèque 3D JavaScript
- **HTML5/CSS3** : Structure et style
- **JavaScript ES6** : Logique de l'application

## Structure du projet

```
galerie/
├── index.html          # Page principale
├── style.css           # Styles CSS
├── script.js           # Logique Three.js
└── README.md           # Documentation
```

## Installation

Aucune installation requise ! Ouvrez simplement `index.html` dans un navigateur web moderne.

**Note** : Une connexion internet est nécessaire pour charger Three.js et les images des œuvres d'art.

## Améliorations possibles

- Ajout de plus d'œuvres d'art
- Contrôles de déplacement WASD
- Sons d'ambiance
- Animations des œuvres
- Mode VR
- Sculptures 3D sur piédestaux
