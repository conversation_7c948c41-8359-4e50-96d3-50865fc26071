// Variables globales
let scene, camera, renderer, controls;
let artworks = [];
let raycaster, mouse;

// Données des œuvres d'art
const artworkData = [
    {
        title: "<PERSON> Joconde",
        artist: "<PERSON><PERSON>",
        description: "Portrait emblématique de la Renaissance italienne, célèbre pour son sourire énigmatique.",
        imageUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/e/ec/<PERSON>_Lisa%2C_by_<PERSON>_<PERSON>_<PERSON>%2C_from_C2RMF_retouched.jpg/687px-<PERSON>_Lisa%2C_by_<PERSON>_<PERSON>_<PERSON>%2C_from_C2RMF_retouched.jpg"
    },
    {
        title: "La Nuit étoilée",
        artist: "<PERSON>",
        description: "Œuvre post-impressionniste représentant un paysage nocturne tourbillonnant.",
        imageUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/e/ea/<PERSON>_<PERSON>_-_Starry_Night_-_Google_Art_Project.jpg/1280px-<PERSON>_<PERSON>_-_Starry_Night_-_Google_Art_Project.jpg"
    },
    {
        title: "Le Cri",
        artist: "<PERSON><PERSON>",
        description: "Expression de l'angoisse existentielle moderne, devenue icône de l'art contemporain.",
        imageUrl: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Edvard_Munch%2C_1893%2C_The_Scream%2C_oil%2C_tempera_and_pastel_on_cardboard%2C_91_x_73_cm%2C_National_Gallery_of_Norway.jpg/471px-Edvard_Munch%2C_1893%2C_The_Scream%2C_oil%2C_tempera_and_pastel_on_cardboard%2C_91_x_73_cm%2C_National_Gallery_of_Norway.jpg"
    },
    {
        title: "Guernica",
        artist: "Pablo Picasso",
        description: "Œuvre cubiste dénonçant les horreurs de la guerre civile espagnole.",
        imageUrl: "https://upload.wikimedia.org/wikipedia/en/thumb/7/74/PicassoGuernica.jpg/1200px-PicassoGuernica.jpg"
    }
];

// Initialisation
function init() {
    console.log('Initializing gallery...');

    // Vérifier si THREE.js est chargé
    if (typeof THREE === 'undefined') {
        console.error('THREE.js not loaded!');
        return;
    }

    // Création de la scène
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x222222);
    console.log('Scene created');

    // Caméra
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 1.6, 5);
    console.log('Camera created');

    // Renderer
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('container').appendChild(renderer.domElement); // Append to #container
    console.log('Renderer created and added to #container');

    // Contrôles de la caméra (vérifier si OrbitControls est disponible)
    if (typeof THREE.OrbitControls !== 'undefined') {
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.maxPolarAngle = Math.PI / 2;
        controls.minDistance = 2;
        controls.maxDistance = 20;
        console.log('OrbitControls initialized');
    } else {
        console.warn('OrbitControls not available, using basic camera');
    }

    // Raycaster pour les interactions
    raycaster = new THREE.Raycaster();
    mouse = new THREE.Vector2();

    // Éclairage
    setupLighting();
    console.log('Lighting setup complete');

    // Test cube pour vérifier que le rendu fonctionne
    const testGeometry = new THREE.BoxGeometry(1, 1, 1);
    const testMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
    const testCube = new THREE.Mesh(testGeometry, testMaterial);
    testCube.position.set(0, 2, 0);
    scene.add(testCube);
    console.log('Test cube added');

    // Création de la galerie
    createGallery();
    console.log('Gallery created');

    // Ajout des œuvres d'art
    createArtworks();
    console.log('Artworks created');

    // Event listeners
    setupEventListeners();

    // Masquer le loading
    document.getElementById('loading').style.display = 'none';

    // Démarrer l'animation
    animate();
    console.log('Animation started');
}

function setupLighting() {
    // Lumière ambiante
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // Lumière directionnelle principale
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // Lumières ponctuelles pour éclairer les œuvres
    const spotLight1 = new THREE.SpotLight(0xffffff, 1, 30, Math.PI / 6, 0.3);
    spotLight1.position.set(-8, 8, 0);
    spotLight1.target.position.set(-8, 0, 0);
    scene.add(spotLight1);
    scene.add(spotLight1.target);

    const spotLight2 = new THREE.SpotLight(0xffffff, 1, 30, Math.PI / 6, 0.3);
    spotLight2.position.set(8, 8, 0);
    spotLight2.target.position.set(8, 0, 0);
    scene.add(spotLight2);
    scene.add(spotLight2.target);
}

function createGallery() {
    // Sol
    const floorGeometry = new THREE.PlaneGeometry(20, 20);
    const floorMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.receiveShadow = true;
    scene.add(floor);

    // Murs
    const wallMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });

    // Mur arrière
    const backWallGeometry = new THREE.PlaneGeometry(20, 10);
    const backWall = new THREE.Mesh(backWallGeometry, wallMaterial);
    backWall.position.set(0, 5, -10);
    scene.add(backWall);

    // Mur gauche
    const leftWallGeometry = new THREE.PlaneGeometry(20, 10);
    const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial);
    leftWall.position.set(-10, 5, 0);
    leftWall.rotation.y = Math.PI / 2;
    scene.add(leftWall);

    // Mur droit
    const rightWallGeometry = new THREE.PlaneGeometry(20, 10);
    const rightWall = new THREE.Mesh(rightWallGeometry, wallMaterial);
    rightWall.position.set(10, 5, 0);
    rightWall.rotation.y = -Math.PI / 2;
    scene.add(rightWall);

    // Plafond
    const ceilingGeometry = new THREE.PlaneGeometry(20, 20);
    const ceilingMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
    const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
    ceiling.position.y = 10;
    ceiling.rotation.x = Math.PI / 2;
    scene.add(ceiling);
}

function createArtworks() {
    // Positions des œuvres sur les murs
    const positions = [
        { x: -8, y: 3, z: -9.8, rotation: 0 },    // Mur arrière gauche
        { x: 8, y: 3, z: -9.8, rotation: 0 },     // Mur arrière droit
        { x: -9.8, y: 3, z: -5, rotation: Math.PI / 2 },  // Mur gauche
        { x: -9.8, y: 3, z: 5, rotation: Math.PI / 2 }    // Mur gauche
    ];

    // Couleurs de fallback pour chaque œuvre
    const fallbackColors = [0xff6b6b, 0x4ecdc4, 0x45b7d1, 0xf9ca24];

    artworkData.forEach((artwork, index) => {
        if (index < positions.length) {
            createPainting(artwork, positions[index], index, fallbackColors[index]);
        }
    });
}

function createPainting(artworkInfo, position, index, fallbackColor = 0x888888) {
    // Cadre - créé immédiatement
    const frameGeometry = new THREE.BoxGeometry(3.2, 2.4, 0.1);
    const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);

    // Positionnement du cadre
    frame.position.set(position.x, position.y, position.z);
    frame.rotation.y = position.rotation;
    frame.castShadow = true;
    scene.add(frame);

    // Toile - créée avec couleur de fallback d'abord
    const canvasGeometry = new THREE.PlaneGeometry(3, 2.2);
    const fallbackMaterial = new THREE.MeshLambertMaterial({ color: fallbackColor });
    const canvas = new THREE.Mesh(canvasGeometry, fallbackMaterial);

    // Positionnement de la toile
    canvas.position.set(position.x, position.y, position.z + 0.06);
    canvas.rotation.y = position.rotation;
    canvas.castShadow = true;

    // Ajout des propriétés pour l'interaction
    canvas.userData = {
        type: 'artwork',
        info: artworkInfo,
        index: index
    };

    scene.add(canvas);
    artworks.push(canvas);

    // Tentative de chargement de l'image réelle
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(
        artworkInfo.imageUrl,
        function (texture) {
            // Remplacer le matériau par l'image chargée
            canvas.material = new THREE.MeshLambertMaterial({ map: texture });
            console.log(`Image chargée pour: ${artworkInfo.title}`);
        },
        function (progress) {
            console.log(`Chargement de ${artworkInfo.title}: ${(progress.loaded / progress.total * 100)}%`);
        },
        function (error) {
            console.error(`Erreur lors du chargement de l'image pour ${artworkInfo.title}:`, error);
            // Garder la couleur de fallback
        }
    );
}

function setupEventListeners() {
    // Redimensionnement de la fenêtre
    window.addEventListener('resize', onWindowResize, false);

    // Clics sur les œuvres
    renderer.domElement.addEventListener('click', onMouseClick, false);

    // Fermeture du panneau d'information
    document.getElementById('close-info').addEventListener('click', closeInfoPanel);
}

function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

function onMouseClick(event) {
    // Calcul de la position de la souris
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    // Raycasting
    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObjects(artworks);

    if (intersects.length > 0) {
        const artwork = intersects[0].object;
        if (artwork.userData.type === 'artwork') {
            showArtworkInfo(artwork.userData.info);
        }
    }
}

function showArtworkInfo(artworkInfo) {
    document.getElementById('artwork-title').textContent = artworkInfo.title;
    document.getElementById('artist-name').textContent = artworkInfo.artist;
    document.getElementById('artwork-description').textContent = artworkInfo.description;
    document.getElementById('info-panel').classList.remove('hidden');
}

function closeInfoPanel() {
    document.getElementById('info-panel').classList.add('hidden');
}

function animate() {
    requestAnimationFrame(animate);

    // Mettre à jour les contrôles seulement s'ils existent
    if (controls && controls.update) {
        controls.update();
    }

    renderer.render(scene, camera);
}

// Démarrage de l'application
window.addEventListener('load', init);
